# 🤖 Botsito Backend

**WhatsApp Bot para Gestión de Citas** - Backend Node.js con BuilderBot y Firebase

## 📋 Descripción

Botsito es un backend de WhatsApp bot diseñado para gestionar citas de manera automatizada. Utiliza BuilderBot para la integración con WhatsApp Business API y Firebase Firestore como base de datos.

### ✨ Características Principales

- 🔥 **Firebase Firestore** - Base de datos en tiempo real
- 📱 **WhatsApp Business API** - Integración con BuilderBot
- 📅 **Zoho Calendar** - Integración opcional para sincronización
- 🔐 **Seguridad Avanzada** - Cifrado AES-256-GCM para tokens OAuth
- 🔒 **Validación robusta** - Variables de entorno con Zod
- 🚀 **Fácil despliegue** - Configuración con variables de entorno
- 📊 **API REST** - Endpoints para gestión de citas
- 🔄 **Migración Automática** - Upgrade de seguridad sin downtime

## 🚀 Inicio Rápido

### 1. Prerrequisitos

- Node.js 18+
- Cuenta Firebase con Firestore habilitado
- WhatsApp Business Account (opcional para desarrollo)

### 2. Instalación

```bash
# Clonar el repositorio
git clone <repository-url>
cd botsito_be

# Instalar dependencias
npm install

# Configurar variables de entorno
npm run setup
```

### 3. Configuración de Variables de Entorno

El comando `npm run setup` creará un archivo `.env` desde `.env.example`. Necesitas configurar:

#### 🔥 Variables Firebase (REQUERIDAS)

```env
FIREBASE_PROJECT_ID=tu-proyecto-firebase
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nTU_CLAVE_PRIVADA\n-----END PRIVATE KEY-----\n"
```

#### 🗂️ Variables Firestore (REQUERIDAS)

```env
COLLECTION=companies
DOC_ID=tu-documento-id
```

#### ⚙️ Variables Opcionales

```env
PORT=3008
ZOHO_CLIENT_ID=tu-zoho-client-id
ZOHO_CLIENT_SECRET=tu-zoho-secret
ZOHO_REDIRECT_URL=http://localhost:3000/oauth2callback
ZOHO_CALENDAR_UID=tu-calendar-uid
```

### 4. Obtener Credenciales Firebase

1. Ve a [Firebase Console](https://console.firebase.google.com)
2. Selecciona tu proyecto o crea uno nuevo
3. Ve a **Project Settings > Service Accounts**
4. Click en **"Generate new private key"**
5. Descarga el archivo JSON y extrae los valores para las variables de entorno

### 5. Ejecutar la Aplicación

```bash
# Desarrollo
npm run dev

# Producción
npm run build
npm start

# Verificar Firebase
npm run test:firebase
```

## 📚 Scripts Disponibles

| Comando                          | Descripción                                       |
| -------------------------------- | ------------------------------------------------- |
| `npm run dev`                    | Ejecuta en modo desarrollo con hot reload         |
| `npm run build`                  | Compila el proyecto para producción               |
| `npm start`                      | Ejecuta la aplicación compilada                   |
| `npm run setup`                  | Configura variables de entorno desde .env.example |
| `npm run test:firebase`          | Verifica conectividad con Firebase                |
| `npm run lint`                   | Ejecuta ESLint                                    |
| `npm test`                       | Ejecuta suite de pruebas de seguridad             |
| `npm run regenerate-zoho-tokens` | Regenera tokens de Zoho cuando expiren            |

## 🔐 Sistema de Seguridad de Tokens Zoho

### ✨ Características de Seguridad

El sistema incluye **cifrado de grado militar** para proteger los tokens OAuth de Zoho Calendar:

- 🔐 **Cifrado AES-256-GCM** - Protección con autenticación
- 🔄 **Migración Automática** - Upgrade transparente sin downtime
- 📦 **Backup Cifrado** - Respaldo automático en Firebase
- ⏰ **Monitoreo Proactivo** - Alertas de expiración
- 🔑 **Rotación de Claves** - Renovación periódica de seguridad
- 📝 **Auditoría Completa** - Logging de operaciones
- 🧪 **Testing Exhaustivo** - Suite completa de pruebas

### 🚀 Configuración de Seguridad

#### 1. Variables de Entorno Requeridas

```env
# Clave de cifrado para tokens (generar nueva)
ZOHO_TOKEN_ENCRYPTION_KEY=<clave-base64-32-bytes>

# Habilitar backup automático
ZOHO_TOKEN_BACKUP_ENABLED=true

# Clave JWT (mínimo 32 caracteres)
JWT_SECRET=<tu-secreto-jwt-seguro>
```

#### 2. Generar Clave de Cifrado

```bash
# Generar nueva clave de 32 bytes en base64
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

#### 3. Configuración Automática

El sistema detecta automáticamente tokens existentes y los migra:

```bash
# La migración ocurre automáticamente al iniciar
npm start

# Verificar estado de migración
ls src/calendar/
# Verás: tokens.json, tokens.encrypted.json, migration.log
```

### 🧪 Comandos de Prueba

#### Pruebas Básicas

```bash
# Verificar configuración y cifrado básico
node -e "
require('dotenv').config();
const crypto = require('crypto');
const key = process.env.ZOHO_TOKEN_ENCRYPTION_KEY;
if (key && Buffer.from(key, 'base64').length === 32) {
  console.log('✅ Configuración de seguridad OK');
} else {
  console.log('❌ Configurar ZOHO_TOKEN_ENCRYPTION_KEY');
}
"
```

#### Pruebas de Migración

```bash
# Verificar migración de tokens
node -e "
const fs = require('fs');
const hasPlain = fs.existsSync('src/calendar/tokens.json');
const hasEncrypted = fs.existsSync('src/calendar/tokens.encrypted.json');
const hasMigration = fs.existsSync('src/calendar/migration.log');
console.log('📄 Tokens planos:', hasPlain ? '✅' : '❌');
console.log('🔐 Tokens cifrados:', hasEncrypted ? '✅' : '❌');
console.log('📝 Log migración:', hasMigration ? '✅' : '❌');
"
```

#### Suite Completa de Pruebas

```bash
# Ejecutar todas las pruebas de seguridad
npm test

# Pruebas específicas
npm test -- --testPathPattern=security
npm test -- --testPathPattern=zohoApi
```

### 📊 Monitoreo y Mantenimiento

#### Verificar Estado de Tokens

```bash
# Estado actual del sistema
node -e "
require('dotenv').config();
const fs = require('fs');
if (fs.existsSync('src/calendar/tokens.encrypted.json')) {
  const data = JSON.parse(fs.readFileSync('src/calendar/tokens.encrypted.json'));
  console.log('🔐 Tokens cifrados encontrados');
  console.log('📅 Timestamp:', new Date(data.timestamp).toISOString());
  console.log('🔧 Algoritmo:', data.algorithm);
} else {
  console.log('ℹ️ No hay tokens cifrados');
}
"
```

#### Backup y Recuperación

```bash
# Verificar backups en Firebase
# (Los backups se crean automáticamente)

# En caso de emergencia, consultar:
cat docs/ZOHO_TOKEN_RECOVERY.md
```

#### Regenerar Tokens Expirados

```bash
# Si ves error "REFRESH TOKEN EXPIRADO"
npm run regenerate-zoho-tokens

# El script te guiará paso a paso:
# 1. Ve a Zoho API Console
# 2. Genera nuevo authorization code
# 3. Ingresa el código en el script
# 4. Los tokens se guardan automáticamente cifrados
```

### 🛡️ Beneficios de Seguridad

| Antes                    | Después                      |
| ------------------------ | ---------------------------- |
| ❌ Tokens en texto plano | ✅ Cifrado AES-256-GCM       |
| ❌ Sin backup            | ✅ Backup automático cifrado |
| ❌ Sin monitoreo         | ✅ Alertas de expiración     |
| ❌ Sin auditoría         | ✅ Logging completo          |
| ❌ Vulnerable a lectura  | ✅ Protección militar        |

### 📁 Archivos de Seguridad

```
src/calendar/
├── secureTokenStorage.ts     # Servicio de cifrado AES-256-GCM
├── tokenManager.ts           # Gestión con migración automática
├── tokenBackupService.ts     # Backup cifrado en Firebase
├── tokenMonitor.ts           # Monitoreo de expiración
├── tokenRotationService.ts   # Rotación de claves
├── migrationTools.ts         # Herramientas de migración
├── tokens.json              # Tokens originales (migrados)
├── tokens.encrypted.json    # Tokens cifrados (nuevo)
├── migration.log            # Log de migración
└── __tests__/               # Suite de pruebas
    ├── securityComponents.test.ts
    ├── zohoApiIntegration.test.ts
    └── securityValidation.test.ts
```

## 🔧 Arquitectura

```
src/
├── app.ts                 # Punto de entrada principal
├── config/
│   ├── envConfig.ts       # Configuración de variables de entorno
│   └── envValidation.ts   # Validación con Zod (incluye seguridad)
├── database/
│   ├── firebaseConfig.ts  # Configuración Firebase
│   └── firebaseService.ts # Servicios de Firestore
├── calendar/              # 🔐 Sistema de Seguridad Zoho
│   ├── secureTokenStorage.ts    # Cifrado AES-256-GCM
│   ├── tokenManager.ts          # Gestión con migración automática
│   ├── tokenBackupService.ts    # Backup cifrado en Firebase
│   ├── tokenMonitor.ts          # Monitoreo de expiración
│   ├── tokenRotationService.ts  # Rotación de claves
│   ├── migrationTools.ts        # Herramientas de migración
│   ├── ClassZohoApi.ts          # API Zoho (actualizada)
│   └── __tests__/               # Suite de pruebas de seguridad
├── flows/                 # Flujos de conversación del bot
├── controllers/           # Controladores de API REST
├── model/                 # Modelos de datos TypeScript
└── utils/                 # Utilidades y helpers
```

## 🔧 Regeneración de Tokens Zoho

### ¿Cuándo necesitas regenerar tokens?

- **🔄 Automático**: Los tokens se renuevan solos cada hora
- **⚠️ Manual**: Solo cuando el refresh token expira (cada varios meses) o hay problemas técnicos

### Pasos para regenerar tokens manualmente

1. **Generar código en Zoho Console**:
   - Ve a: https://api-console.zoho.com/
   - Selecciona tu aplicación → "Self Client" → "Generate Code"
   - Scopes: `ZohoCalendar.calendar.ALL`
   - Copia el código (úsalo inmediatamente, expira en 10 min)

2. **Usar el endpoint de regeneración**:
   ```bash
   curl -X POST http://localhost:3008/v1/zoho/regenerate-tokens \
     -H "Content-Type: application/json" \
     -d '{"authorizationCode": "TU_CODIGO_AQUI"}'
   ```

3. **Verificar que funciona**:
   ```bash
   curl http://localhost:3008/v1/events
   ```

### ⚡ Respuesta exitosa
```json
{
  "success": true,
  "message": "Tokens de Zoho regenerados exitosamente",
  "tokenInfo": {
    "accessTokenPreview": "1000.xxx...",
    "refreshTokenPreview": "Nuevo refresh token guardado",
    "expiresInHours": 1
  }
}
```

## 🌐 API Endpoints

### Gestión de Tokens

- `POST /v1/zoho/regenerate-tokens` - Regenerar tokens de Zoho

### Gestión de Citas

- `POST /v1/appointments/cancel` - Cancelar cita
- `POST /v1/appointments/delete` - Eliminar cita
- `POST /v1/appointments/accept` - Aceptar cita
- `POST /v1/appointments/update` - Actualizar cita
- `GET /v1/appointments/available-hours` - Horas disponibles
- `GET /v1/appointments/validate-date` - Validar fecha

### Gestión de Mensajes

- `POST /v1/message` - Enviar mensaje
- `POST /v1/blacklist` - Gestionar lista negra

### Utilidades

- `GET /v1/qr` - Obtener código QR de WhatsApp
- `GET /v1/events` - Obtener eventos del calendario

## 🔒 Seguridad

- ✅ **Variables de entorno** - Credenciales no hardcodeadas
- ✅ **Validación robusta** - Zod para validación de tipos
- ✅ **Firebase Admin SDK** - Autenticación segura
- ✅ **Gitignore configurado** - Archivos sensibles excluidos

## 🚀 Despliegue

### Railway/Render

1. Conecta tu repositorio
2. Configura las variables de entorno en el panel
3. Despliega automáticamente

### Docker

```bash
# Construir imagen
docker build -t botsito-backend .

# Ejecutar contenedor
docker run -p 3008:3008 --env-file .env botsito-backend
```

## 🛠️ Desarrollo

### Estructura de Datos Firebase

```
companies/{DOC_ID}/
├── appointments/          # Citas agendadas
├── clients/              # Información de clientes
├── users/                # Usuarios del sistema
└── settings/             # Configuraciones
```

### Agregar Nuevos Flujos

1. Crear archivo en `src/flows/`
2. Importar en `src/flows/index.ts`
3. Configurar palabras clave y respuestas

## 📋 TODO

- [ ] Crear método de validación completa para agendar citas
- [ ] Mejorar manejo de estados de citas (aceptadas/canceladas)
- [ ] Implementar Cloud Function para limpiar citas canceladas
- [ ] Agregar indicador "Escribiendo..."
- [ ] Implementar caché para consultas frecuentes
- [ ] Revisar flujo completo de agendamiento
- [ ] Agregar mas tiempo de delay a los mensaje (aumentar el tiempo en el helper)
- [ ] Usar un numero de telefono en whats por 6 meses

## 🤝 Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit tus cambios (`git commit -m 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.
