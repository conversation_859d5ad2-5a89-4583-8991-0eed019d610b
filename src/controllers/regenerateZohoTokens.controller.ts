import { BotType, RequestType, ResponseType } from '../model/BotModel';
import zohoInstance from '../calendar/ClassZohoApi';

interface RegenerateTokensRequest {
  authorizationCode: string;
}

interface RegenerateTokensResponse {
  success: boolean;
  message: string;
  tokenInfo?: {
    accessTokenPreview: string;
    refreshTokenPreview: string;
    expiresInHours: number;
  };
}

/**
 * Controlador para regenerar tokens de Zoho usando un nuevo código de autorización
 * Útil cuando el refresh token expira y necesitas usar un nuevo código de Zoho Console
 */
export const regenerateZohoTokensController = async (
  bot: BotType,
  req: RequestType<RegenerateTokensRequest>,
  res: ResponseType
) => {
  try {
    const { authorizationCode } = req.body;

    // Validar que se proporcione el código de autorización
    if (!authorizationCode || authorizationCode.trim().length === 0) {
      const errorResponse: RegenerateTokensResponse = {
        success: false,
        message: 'Código de autorización requerido',
      };
      
      res.writeHead(400, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(errorResponse));
      return;
    }

    console.log('🔄 Regenerando tokens de Zoho con nuevo código de autorización...');

    // Usar el método existente de la clase ZohoApi
    const accessToken = await zohoInstance.firstConnectionWithAPIZoho(authorizationCode.trim());

    // Si llegamos aquí, los tokens se regeneraron exitosamente
    const successResponse: RegenerateTokensResponse = {
      success: true,
      message: 'Tokens de Zoho regenerados exitosamente',
      tokenInfo: {
        accessTokenPreview: `${accessToken.substring(0, 20)}...`,
        refreshTokenPreview: 'Nuevo refresh token guardado',
        expiresInHours: 1, // Los tokens de Zoho típicamente expiran en 1 hora
      },
    };

    console.log('✅ Tokens de Zoho regenerados exitosamente');
    
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(successResponse));

  } catch (error: any) {
    console.error('❌ Error al regenerar tokens de Zoho:', error);

    let errorMessage = 'Error interno del servidor';
    let statusCode = 500;

    // Manejar errores específicos de Zoho
    if (error?.error === 'invalid_code') {
      errorMessage = 'Código de autorización inválido o expirado. Genera un nuevo código en Zoho Console.';
      statusCode = 400;
    } else if (error?.error === 'invalid_client') {
      errorMessage = 'Configuración de cliente Zoho inválida. Verifica ZOHO_CLIENT_ID y ZOHO_CLIENT_SECRET.';
      statusCode = 400;
    } else if (error?.error === 'invalid_grant') {
      errorMessage = 'Grant inválido. El código de autorización puede haber sido usado anteriormente.';
      statusCode = 400;
    } else if (error?.message) {
      errorMessage = error.message;
    }

    const errorResponse: RegenerateTokensResponse = {
      success: false,
      message: errorMessage,
    };

    res.writeHead(statusCode, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(errorResponse));
  }
};
